"use client";

import React, { useEffect } from "react";
import { requestForToken } from "@/libs/firebase";
import { STORAGE_KEYS, StorageUtils } from "@/constants/storage";

interface NotificationPermissionProps {
    onPermissionHandled?: (granted: boolean) => void;
}

export const NotificationPermission: React.FC<NotificationPermissionProps> = ({
    onPermissionHandled
}) => {
    useEffect(() => {
        if (typeof window === 'undefined') return;

        const requestNotificationPermission = async () => {
            const isRegistered = StorageUtils.hasItem(STORAGE_KEYS.FCM_TOKEN_REGISTERED);

            if (!isRegistered && Notification.permission === 'default') {
                try {
                    const token = await requestForToken();

                    if (token) {
                        onPermissionHandled?.(true);
                    } else {
                        onPermissionHandled?.(false);
                    }
                } catch (error) {
                    onPermissionHandled?.(false);
                }
            } else if (Notification.permission === 'granted' && !isRegistered) {
                try {
                    const token = await requestForToken();

                    if (token) {
                        onPermissionHandled?.(true);
                    }
                } catch (error) {
                    onPermissionHandled?.(false);
                }
            } else if (Notification.permission === 'denied') {
                onPermissionHandled?.(false);
            }
        };

        const timeoutId = setTimeout(() => {
            requestNotificationPermission();
        }, 2000);

        return () => clearTimeout(timeoutId);
    }, []);

    return null;
};
