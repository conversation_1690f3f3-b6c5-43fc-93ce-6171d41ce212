import { initializeApp } from "firebase/app";
import { getMessaging, getToken } from "firebase/messaging";
import { ApiService } from "@/api/axios";
import { ENV_CONFIG, STORAGE_KEYS, StorageUtils } from "@/constants/storage";

const firebaseConfig = {
    apiKey: ENV_CONFIG.FIREBASE.API_KEY,
    authDomain: ENV_CONFIG.FIREBASE.AUTH_DOMAIN,
    projectId: ENV_CONFIG.FIREBASE.PROJECT_ID,
    storageBucket: ENV_CONFIG.FIREBASE.STORAGE_BUCKET,
    messagingSenderId: ENV_CONFIG.FIREBASE.MESSAGING_SENDER_ID,
    appId: ENV_CONFIG.FIREBASE.APP_ID,
    measurementId: ENV_CONFIG.FIREBASE.MEASUREMENT_ID,
};

const app = initializeApp(firebaseConfig);

export const messaging = typeof window !== 'undefined' ? getMessaging(app) : null;

let isRequestingToken = false;

export const requestForToken = async () => {
    if (typeof window === 'undefined' || !messaging) return null;

    if (isRequestingToken) return null;

    isRequestingToken = true;

    try {
        const registrations = await navigator.serviceWorker.getRegistrations();
        for (let registration of registrations) {
            await registration.unregister();
        }

        const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
        await navigator.serviceWorker.ready;

        let permission = Notification.permission;
        if (permission === 'default') {
            permission = await Notification.requestPermission();
        }

        if (permission === 'granted') {
            const token = await getToken(messaging, {
                vapidKey: ENV_CONFIG.FIREBASE.VAPID_KEY,
                serviceWorkerRegistration: registration
            });

            if (token) {
                try {
                    await ApiService.updateFcmToken(token);
                    StorageUtils.setItem(STORAGE_KEYS.FCM_TOKEN_REGISTERED, 'true');
                    return token;
                } catch (error) {
                    throw error;
                }
            } else {
                return null;
            }
        } else {
            return null;
        }
    } catch (error) {
        throw error;
    } finally {
        isRequestingToken = false;
    }
};
