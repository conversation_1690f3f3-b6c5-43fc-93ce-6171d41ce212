import React, { useState, useEffect } from 'react';
import { XMarkIcon, ChevronLeftIcon, ChevronRightIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface ImagePreviewModalProps {
    images: string[];
    initialIndex: number;
    isOpen: boolean;
    onClose: () => void;
}

export const ImagePreviewModal: React.FC<ImagePreviewModalProps> = ({
    images,
    initialIndex,
    isOpen,
    onClose
}) => {
    const [currentIndex, setCurrentIndex] = useState(initialIndex);

    useEffect(() => {
        setCurrentIndex(initialIndex);
    }, [initialIndex]);

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (!isOpen) return;

            switch (e.key) {
                case 'Escape':
                    onClose();
                    break;
                case 'ArrowLeft':
                    goToPrevious();
                    break;
                case 'ArrowRight':
                    goToNext();
                    break;
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [isOpen, currentIndex]);

    const goToPrevious = () => {
        setCurrentIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1));
    };

    const goToNext = () => {
        setCurrentIndex((prev) => (prev < images.length - 1 ? prev + 1 : 0));
    };

    const extractFilename = (url: string, fallback: string): string => {
        try {
            const urlParts = url.split('/');
            const encodedFilename = urlParts[urlParts.length - 1];
            if (!encodedFilename) return fallback;

            // Decode and remove query parameters
            const decodedName = decodeURIComponent(encodedFilename).split('?')[0];
            return decodedName || fallback;
        } catch (e) {
            return fallback;
        }
    };

    const downloadImage = (url: string, index: number) => {
        try {
            if (!url) {
                toast.error('Invalid image URL');
                return;
            }

            const link = document.createElement('a');
            link.href = url;
            const fileName = extractFilename(url, `image-${index + 1}-${Date.now()}.jpg`);
            link.download = fileName;
            link.target = '_blank';
            link.rel = 'noopener noreferrer';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            toast.success('Image downloaded successfully!', {
                duration: 3000,
            });
        } catch (error) {
            toast.error('Failed to download image. Please try again.');
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
            <div
                className="absolute inset-0 bg-black/90"
                onClick={onClose}
            />

            <div className="relative z-10 w-screen h-screen flex items-center justify-center">
                {/* Close button */}
                <button
                    onClick={onClose}
                    className="absolute top-6 right-6 z-20 w-12 h-12 bg-white/10 hover:bg-white/20 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 backdrop-blur-sm"
                >
                    <XMarkIcon className="w-6 h-6" />
                </button>

                {/* Download button */}
                <button
                    onClick={() => downloadImage(images[currentIndex], currentIndex)}
                    className="absolute top-6 right-20 z-10 p-2 bg-white/10 hover:bg-white/20 text-white rounded-full transition-colors duration-200 backdrop-blur-sm"
                    title="Download image"
                >
                    <ArrowDownTrayIcon className="w-6 h-6" />
                </button>

                {/* Navigation buttons */}
                {images.length > 1 && (
                    <>
                        <button
                            onClick={goToPrevious}
                            className="absolute left-6 top-1/2 transform -translate-y-1/2 z-20 w-14 h-14 bg-white/10 hover:bg-white/20 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 backdrop-blur-sm"
                        >
                            <ChevronLeftIcon className="w-7 h-7" />
                        </button>

                        <button
                            onClick={goToNext}
                            className="absolute right-6 top-1/2 transform -translate-y-1/2 z-20 w-14 h-14 bg-white/10 hover:bg-white/20 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 backdrop-blur-sm"
                        >
                            <ChevronRightIcon className="w-7 h-7" />
                        </button>
                    </>
                )}

                {/* Image container */}
                <div className="flex items-center justify-center w-full h-full px-24 py-20">
                    <img
                        src={images[currentIndex]}
                        alt={`Preview ${currentIndex + 1}`}
                        className="max-w-full max-h-full w-auto h-auto object-contain rounded-lg shadow-2xl"
                        onClick={(e) => e.stopPropagation()}
                    />
                </div>

                {/* Image counter and thumbnails */}
                {images.length > 1 && (
                    <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex flex-col items-center gap-3">
                        {/* Counter */}
                        <div className="bg-white/10 text-white px-4 py-2 rounded-full text-sm font-medium backdrop-blur-sm">
                            {currentIndex + 1} / {images.length}
                        </div>

                        {/* Thumbnails */}
                        <div className="flex gap-2 bg-white/10 p-2 rounded-lg backdrop-blur-sm">
                            {images.map((image, index) => (
                                <button
                                    key={index}
                                    onClick={() => setCurrentIndex(index)}
                                    className={`w-12 h-12 rounded-lg overflow-hidden transition-all duration-200 ${index === currentIndex
                                        ? 'ring-2 ring-white scale-110'
                                        : 'opacity-60 hover:opacity-100'
                                        }`}
                                >
                                    <img
                                        src={image}
                                        alt={`Thumbnail ${index + 1}`}
                                        className="w-full h-full object-cover"
                                    />
                                </button>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};
