"use client";

import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { ChatMessage, ConversationCreationResponse, ConnectionStatus } from "@/types/chat";
import { ApiService } from "@/api/axios";
import { webSocketService, ConversationUpdateEvent } from "@/libs/websocket";
import { createChatRequest, sortMessagesByTime } from "@/utils/messageUtils";
import { ChatHeader } from "@/components/ChatHeader";
import { MessageList } from "@/components/MessageList";
import { MessageInput } from "@/components/MessageInput";
import { ChatSidebar } from "@/components/ChatSidebar";
import { NotificationPermission } from "@/components/NotificationPermission";

export default function ChatPage() {
    const [conversations, setConversations] = useState<ConversationCreationResponse[]>([]);
    const [messages, setMessages] = useState<Map<string, ChatMessage[]>>(new Map());
    const [selectedConversation, setSelectedConversation] = useState<string | null>(null);

    const [messagePagination, setMessagePagination] = useState<Map<string, {
        currentPage: number;
        totalPages: number;
        hasMore: boolean;
    }>>(new Map());

    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isClient, setIsClient] = useState(false);
    const loadedConversationsRef = useRef<Set<string>>(new Set());

    const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
        isConnected: false,
        reconnectAttempts: 0
    });

    useEffect(() => {
        setIsClient(true);
    }, []);

    const currentMessages = useMemo(() => {
        return selectedConversation ? messages.get(selectedConversation) || [] : [];
    }, [messages, selectedConversation]);

    const currentConversation = useMemo(() => {
        return conversations.find(conv => conv.id === selectedConversation);
    }, [conversations, selectedConversation]);

    const selectedConversationRef = useRef(selectedConversation);
    selectedConversationRef.current = selectedConversation;

    const handleWebSocketMessage = useCallback((message: ChatMessage) => {
        setMessages(prev => {
            const newMessages = new Map(prev);
            const conversationMessages = newMessages.get(message.conversationId) || [];

            if (message.tempId && message.me) {
                const tempIndex = conversationMessages.findIndex(msg => msg.tempId === message.tempId);
                if (tempIndex !== -1) {
                    const updatedMessages = [...conversationMessages];
                    const existingMessage = updatedMessages[tempIndex];

                    updatedMessages[tempIndex] = {
                        ...existingMessage,
                        ...message,
                        tempId: message.tempId,
                        status: message.status || existingMessage.status,
                        id: message.id || existingMessage.id
                    };

                    newMessages.set(message.conversationId, updatedMessages);
                    return newMessages;
                } else {
                    const tempMessage: ChatMessage = {
                        ...message,
                        id: message.tempId,
                    };
                    const updatedMessages = sortMessagesByTime([...conversationMessages, tempMessage]);
                    newMessages.set(message.conversationId, updatedMessages);
                    return newMessages;
                }
            }

            if (message.tempId && !message.me) {
                return newMessages;
            }

            if (!message.me) {
                const existingIndex = conversationMessages.findIndex(msg => msg.id === message.id);
                if (existingIndex === -1) {
                    const updatedMessages = sortMessagesByTime([...conversationMessages, message]);
                    newMessages.set(message.conversationId, updatedMessages);
                } else {
                    const updatedMessages = [...conversationMessages];
                    updatedMessages[existingIndex] = { ...updatedMessages[existingIndex], ...message };
                    newMessages.set(message.conversationId, updatedMessages);
                }
                return newMessages;
            }

            return newMessages;
        });

        // Auto mark as read for incoming WebSocket messages
        // Backend validates sender - won't mark sender's own messages as read
        const isUnread = !message.read;
        const isViewingConversation = message.conversationId === selectedConversationRef.current;
        const isRealMessage = !message.tempId; // Don't mark temp messages as read

        if (!message.me && isUnread && isViewingConversation && isRealMessage) {
            setTimeout(async () => {
                // Try WebSocket first, fallback to HTTP API
                const success = webSocketService.markAsRead(message.conversationId, message.id);
                if (!success) {
                    try {
                        await ApiService.markAsRead(message.conversationId, message.id);
                    } catch (error) {
                        // Silent error handling
                    }
                }
            }, 500);
        }
    }, []);

    const handleConversationUpdate = useCallback((event: ConversationUpdateEvent) => {
        console.log('🔄 Processing conversation update:', {
            type: event.type,
            conversationId: event.conversation.id,
            triggeredBy: event.userId
        });

        switch (event.type) {
            case 'CONVERSATION_CREATED':
                setConversations(prev => {
                    const existingIndex = prev.findIndex(conv => conv.id === event.conversation.id);

                    if (existingIndex >= 0) {
                        const updated = [...prev];
                        updated[existingIndex] = { ...updated[existingIndex], ...event.conversation };
                        updated.unshift(updated.splice(existingIndex, 1)[0]);
                        return updated;
                    } else {
                        const updated = [event.conversation, ...prev];

                        return updated.sort((a, b) =>
                            new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()
                        );
                    }
                });

                const currentUserId = localStorage.getItem('userId');
                if (!selectedConversation || event.userId === currentUserId) {
                    setSelectedConversation(event.conversation.id);
                }
                break;

            case 'CONVERSATION_UPDATED':
                setConversations(prev =>
                    prev.map(conv =>
                        conv.id === event.conversation.id
                            ? { ...conv, ...event.conversation }
                            : conv
                    )
                );
                break;

            case 'CONVERSATION_DELETED':
                setConversations(prev => {
                    const filtered = prev.filter(conv => conv.id !== event.conversation.id);

                    if (selectedConversation === event.conversation.id) {
                        const nextConversation = filtered.length > 0 ? filtered[0].id : null;
                        setSelectedConversation(nextConversation);
                    }

                    return filtered;
                });

                // Clear messages and pagination for deleted conversation
                setMessages(prev => {
                    const newMessages = new Map(prev);
                    newMessages.delete(event.conversation.id);
                    return newMessages;
                });

                setMessagePagination(prev => {
                    const newPagination = new Map(prev);
                    newPagination.delete(event.conversation.id);
                    return newPagination;
                });
                break;
        }
    }, [selectedConversation]);

    useEffect(() => {

        webSocketService.connect(
            handleWebSocketMessage,     // Real-time messages
            setConnectionStatus,        // Connection status
            handleConversationUpdate    // Real-time conversation updates
        );

        return () => {
            webSocketService.disconnect();
        };
    }, []); // EMPTY DEPS - chỉ connect 1 lần khi mount


    const loadConversations = useCallback(async () => {
        try {
            setLoading(true);

            const data = await ApiService.getConversations();

            const sortedData = data.sort((a, b) =>
                new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()
            );

            setConversations(sortedData);

            if (!webSocketService.isConnected()) {
                webSocketService.forceReconnect();
            } else {
                console.log('🔌 WebSocket check: Already connected ✅');
            }
        } catch (error) {
            console.error('❌ API: Failed to load conversations:', error);
            setError("Không thể tải danh sách cuộc trò chuyện");
        } finally {
            setLoading(false);
        }
    }, []);



    useEffect(() => {
        loadConversations();
    }, [loadConversations]);

    const loadMessages = useCallback(async (conversationId: string, page: number = 1, append: boolean = false) => {
        if (!append && loading) {
            return;
        }
        if (append && loadingMore) {
            return;
        }

        try {
            if (!append) setLoading(true);
            else setLoadingMore(true);

            const pageData = await ApiService.getMessages(conversationId, page);

            setMessages(prev => {
                const newMessages = new Map(prev);
                const existingMessages = prev.get(conversationId) || [];

                if (append) {
                    const combinedMessages = [...pageData.data, ...existingMessages];
                    const sortedMessages = sortMessagesByTime(combinedMessages);
                    newMessages.set(conversationId, sortedMessages);
                } else {
                    const sortedMessages = sortMessagesByTime(pageData.data);
                    newMessages.set(conversationId, sortedMessages);
                }

                return newMessages;
            });
            setMessagePagination(prev => {
                const newPagination = new Map(prev);
                newPagination.set(conversationId, {
                    currentPage: pageData.currentPages,
                    totalPages: pageData.totalPages,
                    hasMore: pageData.currentPages < pageData.totalPages
                });
                return newPagination;
            });

        } catch (error) {
            setError("Không thể tải tin nhắn");
        } finally {
            setLoading(false);
            setLoadingMore(false);
            if (!append) {
                loadedConversationsRef.current.delete(conversationId);
            }
        }
    }, []);


    const handleLoadMore = useCallback(() => {
        if (!selectedConversation) return;

        const pagination = messagePagination.get(selectedConversation);
        if (!pagination || !pagination.hasMore || loadingMore) return;

        const nextPage = pagination.currentPage + 1;
        loadMessages(selectedConversation, nextPage, true);
    }, [selectedConversation, messagePagination, loadingMore]);

    useEffect(() => {
        if (error) {
            const timer = setTimeout(() => setError(null), 5000);
            return () => clearTimeout(timer);
        }
    }, [error]);

    const handleCreateConversation = useCallback(async (userId: string) => {
        try {
            setLoading(true);

            const conversation = await ApiService.createConversation([userId]);

            try {
                await loadMessages(conversation.id, 1, false);
            } catch (messageError) {
                console.warn('⚠️ Failed to load initial messages:', messageError);
            }

            const fallbackTimer = setTimeout(() => {
                setConversations(prev => {
                    const exists = prev.some(conv => conv.id === conversation.id);
                    if (!exists) {
                        const updated = [conversation, ...prev].sort((a, b) =>
                            new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()
                        );
                        setSelectedConversation(conversation.id);
                        return updated;
                    }
                    return prev;
                });
            }, 2000);

            return () => clearTimeout(fallbackTimer);

        } catch (error) {
            console.error('❌ Failed to create conversation:', error);
            setError("Không thể tạo cuộc trò chuyện");
        } finally {
            setLoading(false);
        }
    }, [loadMessages]);

    const handleConversationSelect = useCallback((conversationId: string) => {

        setSelectedConversation(conversationId);
        selectedConversationRef.current = conversationId;

        const existingMessages = messages.get(conversationId);
        if (!existingMessages || existingMessages.length === 0) {
            if (!loadedConversationsRef.current.has(conversationId)) {
                loadedConversationsRef.current.add(conversationId);

                loadMessages(conversationId, 1, false).catch(error => {
                    console.error('❌ API failed to load messages:', error);
                    loadedConversationsRef.current.delete(conversationId);
                });
            }
        } else {
            console.log('📋 Messages already loaded from cache');
        }
    }, [messages, loadMessages]);

    const handleDeleteConversation = useCallback(async (conversationId: string) => {

        try {
            await ApiService.deleteConversation(conversationId);
        } catch (error) {
            console.error('❌ Failed to delete conversation:', error);

            setConversations(prev => prev.filter(conv => conv.id !== conversationId));

            setMessages(prev => {
                const newMessages = new Map(prev);
                newMessages.delete(conversationId);
                return newMessages;
            });

            setMessagePagination(prev => {
                const newPagination = new Map(prev);
                newPagination.delete(conversationId);
                return newPagination;
            });

            if (selectedConversation === conversationId) {
                setSelectedConversation(null);
            }

            throw error;
        }
    }, [selectedConversation]);

    const handleSendMessage = useCallback((messageContent: string, mediaAttachments?: { mediaUrl: string; mediaName: string; mediaSize: number; mediaType: string; displayOrder?: number }[]) => {
        if (!selectedConversation || !connectionStatus.isConnected) return;

        // Convert MediaAttachment to old format for createChatRequest compatibility
        const uploadedFiles = mediaAttachments?.map(attachment => ({
            url: attachment.mediaUrl,
            name: attachment.mediaName,
            size: attachment.mediaSize,
            type: attachment.mediaType,
            displayOrder: attachment.displayOrder
        }));

        const request = createChatRequest(selectedConversation, messageContent, uploadedFiles);
        const success = webSocketService.sendMessage(request);

        if (!success) {
            setError("Không thể gửi tin nhắn. Vui lòng kiểm tra kết nối.");
        }
    }, [selectedConversation, connectionStatus.isConnected]);

    return (
        <div className="flex h-screen overflow-hidden bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
            <div className="absolute inset-0 opacity-30">
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]"></div>
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(139,92,246,0.1),transparent_50%)]"></div>
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(236,72,153,0.1),transparent_50%)]"></div>
            </div>

            <NotificationPermission />

            <div className="relative flex w-full h-full">
                <ChatSidebar
                    conversations={conversations}
                    selectedConversation={selectedConversation}
                    messages={messages}
                    onConversationSelect={handleConversationSelect}
                    onCreateConversation={handleCreateConversation}
                    onDeleteConversation={handleDeleteConversation}
                    onRefreshConversations={loadConversations}
                    loading={loading}
                />

                <div className="flex-1 flex flex-col bg-white/40 backdrop-blur-sm overflow-hidden">
                    <ChatHeader
                        conversation={currentConversation}
                        connectionStatus={connectionStatus}
                    />

                    <div className="flex-1 relative min-h-0">
                        <div className="absolute inset-0 opacity-20">
                            <div className="absolute inset-0" style={{
                                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e2e8f0' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                            }}></div>
                        </div>

                        <MessageList
                            messages={currentMessages}
                            loading={loading}
                            hasMoreMessages={selectedConversation ? messagePagination.get(selectedConversation)?.hasMore || false : false}
                            onLoadMore={handleLoadMore}
                            loadingMore={loadingMore}
                            hasSelectedConversation={!!selectedConversation}
                        />
                    </div>

                    {selectedConversation && (
                        <MessageInput
                            onSendMessage={handleSendMessage}
                            connectionStatus={connectionStatus}
                        />
                    )}
                </div>
            </div>

            {
                isClient && error && (
                    <div className="fixed top-6 right-6 bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-4 rounded-xl shadow-2xl flex items-center gap-3 backdrop-blur-sm border border-red-400/20 z-50">
                        <div className="w-2 h-2 bg-red-200 rounded-full animate-pulse"></div>
                        <span className="font-medium">{error}</span>
                        <button
                            onClick={() => setError(null)}
                            className="text-red-100 hover:text-white transition-colors p-1 rounded-full hover:bg-red-400/20"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                )
            }

        </div >
    );
}