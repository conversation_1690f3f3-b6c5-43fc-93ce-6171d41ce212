<!DOCTYPE html>
<html>
<head>
    <style>
        #root {
            width: 100vw;
            height: 100vh;
        }
    </style>
</head>
<body>
    <div id="root"></div>
</body>
<script src="https://unpkg.com/@zegocloud/zego-uikit-prebuilt/zego-uikit-prebuilt.js"></script>
<script>
window.onload = function () {
    function getUrlParams(url) {
        let urlStr = url.split('?')[1];
        const urlSearchParams = new URLSearchParams(urlStr);
        const result = Object.fromEntries(urlSearchParams.entries());
        return result;
    }

    // Use same config as your app
    const roomID = getUrlParams(window.location.href)['roomID'] || "test_room_123";
    const userID = getUrlParams(window.location.href)['userID'] || Math.floor(Math.random() * 10000) + "";
    const userName = "TestUser" + userID;
    const appID = 1405703260;
    const serverSecret = "94f509acc425a27f12c947b90943f677";
    
    console.log('Test config:', { roomID, userID, userName, appID });
    
    const kitToken = ZegoUIKitPrebuilt.generateKitTokenForTest(appID, serverSecret, roomID, userID, userName);

    const zp = ZegoUIKitPrebuilt.create(kitToken);
    zp.joinRoom({
        container: document.querySelector("#root"),
        sharedLinks: [{
            name: 'Personal link',
            url: window.location.protocol + '//' + window.location.host + window.location.pathname + '?roomID=' + roomID + '&userID=' + userID,
        }],
        scenario: {
            mode: ZegoUIKitPrebuilt.VideoConference,
        },
        
        turnOnMicrophoneWhenJoining: true,
        turnOnCameraWhenJoining: true,
        showMyCameraToggleButton: true,
        showMyMicrophoneToggleButton: true,
        showAudioVideoSettingsButton: true,
        showScreenSharingButton: true,
        showTextChat: true,
        showUserList: true,
        maxUsers: 2,
        layout: "Auto",
        showLayoutButton: false,
    });
}
</script>
</html>
