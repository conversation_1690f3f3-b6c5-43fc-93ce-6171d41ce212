
export const STORAGE_KEYS = {
    NOTIFICATION_PERMISSION_ASKED: 'notification_permission_asked',
    FCM_TOKEN_REGISTERED: 'fcm_token_registered',

    USER_PREFERENCES: 'user_preferences',
    THEME_PREFERENCE: 'theme_preference',

    LAST_CONVERSATION_ID: 'last_conversation_id',
    DRAFT_MESSAGES: 'draft_messages',

    APP_VERSION: 'app_version',
    FIRST_VISIT: 'first_visit',
} as const;


export const ENV_CONFIG = {
    API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080',

    FIREBASE: {
        API_KEY: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || 'AIzaSyCbpWYbgEaamlHgRKkCY9I-j3rPpi1uy1E',
        AUTH_DOMAIN: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || 'fir-push-notification-d802d.firebaseapp.com',
        PROJECT_ID: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'fir-push-notification-d802d',
        STORAGE_BUCKET: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'fir-push-notification-d802d.firebasestorage.app',
        MESSAGING_SENDER_ID: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '584254158278',
        APP_ID: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || '1:584254158278:web:4dec363a775f84d62f6ec7',
        MEASUREMENT_ID: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || 'G-77WDGE653M',
        VAPID_KEY: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || 'BB1EzALF7GSWGywTFfNUq5xHmCFBxR9uYFCQ1qNfoI5S_tiLoPWR67W19q8MkQ3NTCrXrQbuB-t7VAmZ6u542Tw',
    },

    APP: {
        NAME: process.env.NEXT_PUBLIC_APP_NAME || 'Chat Message App',
        VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
        NODE_ENV: process.env.NODE_ENV || 'development',
    }
} as const;


export const StorageUtils = {
    setItem: (key: string, value: string | object): boolean => {
        try {
            if (typeof window === 'undefined') return false;
            const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
            localStorage.setItem(key, stringValue);
            return true;
        } catch (error) {
            return false;
        }
    },

    getItem: (key: string): string | null => {
        try {
            if (typeof window === 'undefined') return null;
            return localStorage.getItem(key);
        } catch (error) {
            return null;
        }
    },


    getJsonItem: <T>(key: string): T | null => {
        try {
            const item = StorageUtils.getItem(key);
            return item ? JSON.parse(item) : null;
        } catch (error) {
            return null;
        }
    },

    removeItem: (key: string): boolean => {
        try {
            if (typeof window === 'undefined') return false;
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            return false;
        }
    },

    hasItem: (key: string): boolean => {
        return StorageUtils.getItem(key) !== null;
    },

    clear: (): boolean => {
        try {
            if (typeof window === 'undefined') return false;
            localStorage.clear();
            return true;
        } catch (error) {
            return false;
        }
    }
};
