import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from 'react-hot-toast';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#ffffff',
              color: '#374151',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '500',
              padding: '12px 16px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              maxWidth: '400px',
              minWidth: '300px',
            },
            success: {
              style: {
                background: '#F0FDF4',
                border: '1px solid #BBF7D0',
                color: '#16A34A',
              },
              iconTheme: {
                primary: '#16A34A',
                secondary: '#F0FDF4',
              },
            },
            error: {
              style: {
                background: '#FEF2F2',
                border: '1px solid #FECACA',
                color: '#DC2626',
              },
              iconTheme: {
                primary: '#DC2626',
                secondary: '#FEF2F2',
              },
            },
          }}
        />
      </body>
    </html>
  );
}
