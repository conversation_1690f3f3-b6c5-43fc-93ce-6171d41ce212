'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import toast from 'react-hot-toast';
import { ApiService } from '@/api/axios';

export default function RegisterPage() {
    const [email, setEmail] = useState('');
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const router = useRouter();

    const handleSubmit = async (e: any) => {
        e.preventDefault();

        if (password !== confirmPassword) {
            toast.error('Mật khẩu không khớp!', {
                position: 'top-right',
                duration: 3000,
            });
            return;
        }

        try {
            await ApiService.register(email, username, password);
            toast.success('Đ<PERSON>ng ký thành công! <PERSON><PERSON> chuyển hướng đến trang đăng nhập...', {
                duration: 2000,
            });
            setTimeout(() => router.push('/auth/login'), 500);
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || 'Đăng ký thất bại. Vui lòng thử lại.';
            toast.error(errorMessage, {
                duration: 3000,
            });
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
            <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-2xl shadow-xl transform transition-all duration-300">
                <div className="flex flex-col items-center gap-3">
                    <img src="/next.svg" alt="Logo" className="w-16 h-16 mb-2 transform hover:scale-105 transition-transform duration-300" />
                    <h2 className="text-4xl font-bold text-gray-900 tracking-tight">Đăng Ký</h2>
                    <p className="text-gray-600 text-sm">Tạo tài khoản mới để bắt đầu!</p>
                </div>
                <form className="space-y-6" onSubmit={handleSubmit}>
                    <div className="space-y-5">
                        <div>
                            <label htmlFor="email" className="block text-sm font-semibold text-gray-800 mb-2">Email</label>
                            <input
                                id="email"
                                name="email"
                                type="email"
                                required
                                className="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white text-gray-900 placeholder-gray-400 transition-all duration-200"
                                placeholder="Nhập email của bạn"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                            />
                        </div>
                        <div>
                            <label htmlFor="username" className="block text-sm font-semibold text-gray-800 mb-2">Tên người dùng</label>
                            <input
                                id="username"
                                name="username"
                                type="text"
                                required
                                className="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white text-gray-900 placeholder-gray-400 transition-all duration-200"
                                placeholder="Nhập tên người dùng"
                                value={username}
                                onChange={(e) => setUsername(e.target.value)}
                            />
                        </div>
                        <div>
                            <label htmlFor="password" className="block text-sm font-semibold text-gray-800 mb-2">Mật khẩu</label>
                            <input
                                id="password"
                                name="password"
                                type="password"
                                required
                                className="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white text-gray-900 placeholder-gray-400 transition-all duration-200"
                                placeholder="Nhập mật khẩu"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                            />
                        </div>
                        <div>
                            <label htmlFor="confirm-password" className="block text-sm font-semibold text-gray-800 mb-2">Xác nhận mật khẩu</label>
                            <input
                                id="confirm-password"
                                name="confirm-password"
                                type="password"
                                required
                                className="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white text-gray-900 placeholder-gray-400 transition-all duration-200"
                                placeholder="Xác nhận mật khẩu"
                                value={confirmPassword}
                                onChange={(e) => setConfirmPassword(e.target.value)}
                            />
                        </div>
                    </div>
                    <button
                        type="submit"
                        className="w-full py-3 rounded-lg bg-gray-900 text-white font-semibold text-lg shadow-lg hover:bg-gray-800 focus:ring-4 focus:ring-gray-300 transition-all duration-300 transform hover:scale-105"
                    >
                        Đăng Ký
                    </button>
                    <div className="text-center text-sm mt-3">
                        <span className="text-gray-600">Đã có tài khoản? </span>
                        <Link href="/auth/login" className="font-semibold text-gray-700 hover:text-gray-900 hover:underline transition-colors duration-200">Đăng nhập ngay</Link>
                    </div>
                </form>
            </div>
        </div>
    );
}