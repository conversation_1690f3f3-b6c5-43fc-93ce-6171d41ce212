
export const ZEGO_CONFIG = {
    appId: 1405703260,
    serverSecret: "94f509acc425a27f12c947b90943f677"
};

export const getCurrentUserId = (): string | null => {
    return localStorage.getItem('userId');
};


export const getCurrentUser = () => {
    const userId = localStorage.getItem('userId');
    const userEmail = localStorage.getItem('userEmail');
    const userName = localStorage.getItem('userName');

    return {
        id: userId,
        email: userEmail,
        name: userName || (userId ? `User_${userId.substring(0, 8)}` : 'Unknown')
    };
};

/**
 * Generate a unique and persistent device ID for the current browser/device
 * The ID will be stored in localStorage and reused across sessions
 */
export const getDeviceId = (): string => {
    let deviceId = localStorage.getItem('deviceId');

    if (!deviceId) {
        const userAgent = window.navigator.userAgent;
        const language = window.navigator.language;
        const screenResolution = `${window.screen.width}x${window.screen.height}`;
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const timestamp = Date.now();

        const randomComponent = Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15);

        const fingerprint = btoa(
            `${userAgent}-${language}-${screenResolution}-${timezone}-${timestamp}-${randomComponent}`
        ).replace(/[^a-zA-Z0-9]/g, '').substring(0, 32);

        deviceId = `device_${fingerprint}`;

        localStorage.setItem('deviceId', deviceId);
    }

    return deviceId;
};


export const clearDeviceId = (): void => {
    localStorage.removeItem('deviceId');
};


export const getDeviceInfo = () => {
    return {
        deviceId: getDeviceId(),
        userAgent: window.navigator.userAgent,
        language: window.navigator.language,
        screenResolution: `${window.screen.width}x${window.screen.height}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        cookieEnabled: window.navigator.cookieEnabled,
        onLine: window.navigator.onLine
    };
};
