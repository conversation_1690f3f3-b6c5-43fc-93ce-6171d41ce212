import { ChatMessage, ChatRequest } from '@/types/chat';
import { FilePreview as FilePreviewType, MessageType, MediaAttachment } from '@/types/file';

/**
 * Utility functions for handling chat messages
 */

/**
 * Generate a unique temporary ID for messages
 */
export const generateTempId = (): string => {
    return `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * Check if a message is temporary (still being sent)
 */
export const isTemporaryMessage = (message: ChatMessage): boolean => {
    return message.status === "SENDING" && !!message.tempId;
};

/**
 * Sort messages by creation time
 */
export const sortMessagesByTime = (messages: ChatMessage[] | null | undefined): ChatMessage[] => {
    // Handle null, undefined, or non-array inputs
    if (!messages || !Array.isArray(messages)) {
        console.warn('sortMessagesByTime received non-array input:', messages);
        return [];
    }

    return [...messages].sort((a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
};

/**
 * Create a temporary message for immediate UI feedback
 */
export const createTemporaryMessage = (
    conversationId: string,
    content: string,
    tempId: string,
    files?: FilePreviewType[],
    currentUsername?: string
): ChatMessage => {
    const mediaUrls = files?.map(file => file.preview || URL.createObjectURL(file.file)) || [];

    return {
        id: tempId, // Use tempId as temporary id until we get real ID
        tempId,
        conversationId,
        me: true,
        username: currentUsername, // Use backend field
        content,
        status: "SENDING",
        createdAt: new Date().toISOString(),
        read: true,
        mediaUrl: mediaUrls.length > 0 ? mediaUrls : undefined,
        messageType: files && files.length > 0 ? "FILE" : "TEXT"
    };
};

/**
 * Create a chat request for sending (with uploaded file URLs)
 * Note: tempId will be generated by backend
 */
export const createChatRequest = (
    conversationId: string,
    content: string,
    uploadedFiles?: { url: string; name: string; size: number; type: string; displayOrder?: number }[]
): ChatRequest => {
    const hasFiles = uploadedFiles && uploadedFiles.length > 0;

    const request: ChatRequest = {
        conversationId,
        content,
        messageType: hasFiles ? MessageType.FILE : MessageType.TEXT
    };

    // Only add mediaAttachments if there are actually files
    if (hasFiles) {
        request.mediaAttachments = uploadedFiles.map((file, index) => ({
            mediaUrl: file.url,
            mediaName: file.name,
            mediaSize: file.size,
            mediaType: file.type,
            displayOrder: file.displayOrder || index + 1
        }));
    }

    return request;
};

/**
 * Update temporary message with server response
 */
export const updateTempMessage = (
    tempMessage: ChatMessage,
    serverMessage: ChatMessage
): ChatMessage => {
    return {
        ...tempMessage,
        id: serverMessage.id,
        status: serverMessage.status,
        read: serverMessage.read,
        createdAt: serverMessage.createdAt,
        mediaAttachments: serverMessage.mediaAttachments, // Update with server media attachments
        tempId: undefined // Remove tempId after update
    };
};

/**
 * Check if two messages are the same (for deduplication)
 */
export const isSameMessage = (msg1: ChatMessage, msg2: ChatMessage): boolean => {
    // Same ID
    if (msg1.id === msg2.id) return true;

    // Same tempId
    if (msg1.tempId && msg2.tempId && msg1.tempId === msg2.tempId) return true;

    // Same content and time (fallback)
    if (msg1.content === msg2.content &&
        msg1.conversationId === msg2.conversationId &&
        Math.abs(new Date(msg1.createdAt).getTime() - new Date(msg2.createdAt).getTime()) < 1000) {
        return true;
    }

    return false;
};
